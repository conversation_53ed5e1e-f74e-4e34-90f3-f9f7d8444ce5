<template>
    <PaperTime v-if="!isExam" />
    <RouterView />
    <DialogExamMediaMonitorHint v-model="dialogVisibleMonitorHintCamera" v-on="{ dialogClosed }" />
    <DialogExamPhoneMonitorHint v-model="dialogVisibleMonitorHintPhone" v-on="{ dialogClosed }" />
    <DialogExamScreenMonitorHint v-model="dialogVisibleMonitorHintScreen" v-on="{ dialogClosed }" />
    <NetworkDetect />
    <div id="body_detect" style="display: none" />
</template>

<script lang="ts" setup>
import DialogExamMediaMonitorHint from '@/components/dialog-exam-media-monitor-hint.vue';
import DialogExamPhoneMonitorHint from '@/components/dialog-exam-phone-monitor-hint.vue';
import DialogExamScreenMonitorHint from '@/components/dialog-exam-screen-monitor-hint.vue';
import NetworkDetect from '@/components/network-detect.vue';
import { useReLoginFlow } from './hooks/useReLoginFlow';
import { watch, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import PaperTime from './components/paper-time.vue';
import { commitPaper, CommitPaperType } from '../utils';
import { useEvaluationEvents } from './hooks/useEvaluationEvents';
import useMonitor from './hooks/useMonitor';
import useFullScreen from './hooks/useFullScreen';
import useSwitchScreen from './hooks/useSwitchScreen';
import useAntiCheat from './hooks/useAntiCheat';
import useDialogHint from './hooks/useDialogHint';
import { useEndTimeStore } from '@/store/exam-time';
import { useMonitorStore } from '@/store/use-monitor-store';
import { componentsMap } from './config';
import { ReplayTypeEnum } from './type';
import { useRouteParamId } from '../hooks/useRouteParamId';

const endTimeStore = useEndTimeStore();
const monitorStore = useMonitorStore();
const $router = useRouter();
const { seqId, examId } = useRouteParamId();

// 使用新的组合式函数
const { onSubmitPaper } = useEvaluationEvents({ seqId, examId });
const { dialogVisibleMonitorHintCamera, dialogVisibleMonitorHintPhone, dialogVisibleMonitorHintScreen, dialogClosed } = useDialogHint();

function initProcess() {
    useMonitor(); // 考试监控初始化完成

    useFullScreen(); // 全屏初始化完成

    useSwitchScreen({
        examId,
        forceSubmitExamFn: () => {
            forceSubmitExam('switchScreenForce');
        },
    }); // '切换屏幕初始化完成'

    useAntiCheat({ encryptExamId: examId }); // 防作弊初始化完成

    useReLoginFlow(); // 重复登录弹窗流程初始化完成
}

const forceSubmitExam = (examSubmitType: CommitPaperType = 'timeout') => {
    if (isExam.value) {
        commitPaper({ type: examSubmitType, seqId, examId });
    } else {
        onSubmitPaper(3); // commitType 3 for timeout
    }
};

watch(
    () => [endTimeStore.examEndTime.remainingTime.total, endTimeStore.examEndTimeStatus],
    ([time, status]) => {
        if (time <= 0 && status === 1) {
            forceSubmitExam('timeout');
        }
    },
);

initProcess();

const type = computed(() => monitorStore.examBaseInfo.examInfo?.examType || ReplayTypeEnum.Exam); // 考试类型
const isExam = computed(() => type.value === ReplayTypeEnum.Exam); // 是否是考试

$router.push(`${componentsMap[type.value as ReplayTypeEnum]}?seqId=${seqId}&examId=${examId}`);

onUnmounted(() => {
    endTimeStore.clearExamEndTime();
});
</script>
