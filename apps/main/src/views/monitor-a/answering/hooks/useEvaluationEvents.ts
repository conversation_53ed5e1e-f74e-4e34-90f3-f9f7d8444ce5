import { ref, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { ExamStatusEnum } from '@crm/exam-types';
import { TCommitType } from '../type';

interface IParams {
    seqId: string;
    examId: string;
}

interface IResult {
    code: number;
    data: any;
}

const submitTypeMap = {
    1: 'click_paper_submit',
    2: 'paper_auto_submit',
    3: 'paper_auto_submit', // 假设超时也用这个 remarks
};

export function useEvaluationEvents({ examId, seqId }: IParams, onSuccess?: (result: IResult) => void, onError?: (result: IResult) => void) {
    const lastQuestionId = ref('');
    const $router = useRouter();

    async function onSubmitPaper(commitType: TCommitType) {
        try {
            const remarks = submitTypeMap[commitType];
            const { code, data } = await Invoke.common.postEvaluationPaperCommit({
                encryptExamId: examId,
                remarks,
                commitType,
            });
            if (code === 0) {
                if (onSuccess) {
                    onSuccess({ code, data });
                } else {
                    $router.push(`/status/${seqId}?status=${ExamStatusEnum.已交卷}&text=您已完成该场考试，剩余${data.pendingCount}场待完成`);
                }
            } else {
                onError && onError({ code, data });
                // 可以考虑抛出错误或进行其他错误处理
            }
        } catch (error) {
            onError && onError({ code: 9999, data: error });
        }
    }

    function onQuestionChange(data: { type?: string; currentQuestionId: string }) {
        const fromId = lastQuestionId.value || '';
        const toId = data.type === 'end' ? '' : data.currentQuestionId;
        if (fromId || toId) {
            // @ts-ignore
            postLog('eval_question_change', {
                encExamId: examId,
                p2: '',
                p3: lastQuestionId.value,
                p4: toId,
            });
        }

        nextTick(() => {
            lastQuestionId.value = toId;
        });
    }

    return {
        onSubmitPaper,
        onQuestionChange,
        lastQuestionId, // 也导出 lastQuestionId 供外部潜在需要
    };
}
